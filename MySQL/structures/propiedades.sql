SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

CREATE TABLE `propiedades` (
    `clave_sistema` bigint(20) NOT NULL,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `contrato` int(9) DEFAULT NULL,
    `status_id` int(5) DEFAULT 1,
    `claveprop` varchar(25) NOT NULL DEFAULT '',
    `nombreprop` varchar(50) NOT NULL,
    `tipo` int(2) UNSIGNED ZEROFILL NOT NULL DEFAULT 00,
    `residencial` enum('Si','No') NOT NULL DEFAULT 'No',
    `comercial` enum('Si','No') NOT NULL DEFAULT 'No',
    `industrial` enum('Si','No') NOT NULL DEFAULT 'No',
    `vacacional` enum('Si','No') NOT NULL DEFAULT 'No',
    `precio_venta` int(12) NOT NULL DEFAULT 0,
    `precio_renta` int(12) NOT NULL DEFAULT 0,
    `precio_diaria` int(12) NOT NULL DEFAULT 0,
    `precio_traspaso` int(12) NOT NULL DEFAULT 0,
    `precio_venta_mxp` double(15,5) DEFAULT NULL,
    `precio_vta_total_mxp` double(15,5) DEFAULT NULL,
    `precio_renta_mxp` double(15,5) DEFAULT NULL,
    `precio_diaria_mxp` double(15,5) DEFAULT NULL,
    `precio_traspaso_mxp` double(15,5) DEFAULT NULL,
    `id_colonia` int(10) NOT NULL DEFAULT 0,
    `colonia` varchar(70) DEFAULT NULL,
    `muestra_colonia` varchar(30) NOT NULL DEFAULT '',
    `zona` varchar(30) DEFAULT NULL,
    `intro_corta_esp` mediumtext DEFAULT NULL,
    `intro_corta_ing` mediumtext DEFAULT NULL,
    `anuncio_esp` longtext DEFAULT NULL,
    `anuncio_ing` longtext DEFAULT NULL,
    `anuncio_fra` longtext DEFAULT NULL,
    `caract_esp` text DEFAULT NULL,
    `caract_ing` mediumtext DEFAULT '',
    `caract_fra` mediumtext DEFAULT '',
    `ciudad` varchar(70) DEFAULT NULL,
    `provincia` varchar(70) DEFAULT NULL,
    `pais` varchar(30) NOT NULL DEFAULT 'México',
    `moneda` char(3) NOT NULL DEFAULT '',
    `enventa` enum('Si','No') NOT NULL DEFAULT 'No',
    `enrenta` enum('Si','No') NOT NULL DEFAULT 'No',
    `endiaria` enum('Si','No') NOT NULL DEFAULT 'No',
    `entraspaso` enum('Si','No') NOT NULL DEFAULT 'No',
    `precio_por_metro` enum('Si','No') NOT NULL DEFAULT 'No',
    `fecha_ingreso` timestamp NOT NULL DEFAULT current_timestamp(),
    `codigo_postal` int(5) UNSIGNED ZEROFILL DEFAULT 00000,
    `operacion_hecha` varchar(10) NOT NULL DEFAULT '',
    `fecha_modificaciones` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
    `eventos` mediumtext DEFAULT NULL COMMENT 'JSON que describe la historia del inmueble',
    `contador` int(5) NOT NULL DEFAULT 0,
    `fecha_expiracion` date NOT NULL DEFAULT '0000-00-00',
    `i_calle_numero` varchar(100) NOT NULL DEFAULT '',
    `i_num_ext` varchar(10) DEFAULT NULL,
    `i_num_int` varchar(10) DEFAULT NULL,
    `i_entre_calles` varchar(100) DEFAULT '',
    `publica_ubicacion` enum('interno','bolsa','web') NOT NULL DEFAULT 'interno',
    `i_propietario` int(9) NOT NULL DEFAULT 0,
    `i_observaciones` mediumtext DEFAULT NULL,
    `i_inventario` mediumtext DEFAULT NULL,
    `comision_ampi` double(5,2) NOT NULL DEFAULT 0.00,
    `sucursal` int(7) DEFAULT NULL,
    `comparto_comision` double(5,2) NOT NULL DEFAULT 0.00,
    `asesor` int(7) NOT NULL DEFAULT 0,
    `i_porcentaje_comision` double(5,2) NOT NULL DEFAULT 0.00,
    `tipo_promocion` enum('DE PALABRA','CARTA AUTORIZACION','EN OPCION','EN EXCLUSIVA') DEFAULT NULL,
    `i_requisito_renta` enum('Fianza','Fiador','Negociable') NOT NULL DEFAULT 'Fiador',
    `desarrollo` int(15) NOT NULL DEFAULT 0,
    `aid` int(7) NOT NULL DEFAULT 0,
    `keywords_esp` varchar(150) NOT NULL DEFAULT '',
    `keywords_ing` varchar(150) NOT NULL DEFAULT '',
    `keywords_fra` varchar(150) NOT NULL DEFAULT '',
    `en_resumen` enum('Si','No') NOT NULL DEFAULT 'No',
    `presentar_con` enum('tour','fotos') NOT NULL DEFAULT 'fotos',
    `status_web` enum('publicado','sin publicar') NOT NULL DEFAULT 'publicado',
    `i_tipo_comision_rta` enum('1 mes','cantidad fija') NOT NULL DEFAULT '1 mes',
    `i_comision_rta` int(9) NOT NULL DEFAULT 0,
    `t_comision_ampi` enum('sobre comision','sobre precio') NOT NULL DEFAULT 'sobre precio',
    `t_comparto_comision` enum('sobre comision','sobre precio') NOT NULL DEFAULT 'sobre comision',
    `rta_comparto_comision` double(5,2) NOT NULL DEFAULT 0.00,
    `rta_comision_ampi` double(5,2) NOT NULL DEFAULT 0.00,
    `fin_contrato` date NOT NULL DEFAULT '0000-00-00',
    `ampi_plus` enum('Si','No','Ya') NOT NULL DEFAULT 'No',
    `conf_portal_ampi` date DEFAULT NULL,
    `msg_conf_portal_ampi` date DEFAULT NULL,
    `clave_externa` varchar(100) DEFAULT NULL,
    `map_lng` varchar(30) DEFAULT NULL,
    `map_lat` varchar(30) DEFAULT NULL,
    `notas_mapa` mediumtext DEFAULT NULL,
    `map_lng_despista` varchar(30) DEFAULT NULL,
    `map_lat_despista` varchar(30) DEFAULT NULL,
    `ocultar_mapa` enum('Si','No') NOT NULL DEFAULT 'No',
    `html_especial` varchar(1024) DEFAULT NULL COMMENT 'Campo para enlace de inserción de videos de youtube por ejemplo',
    `CamposMC` mediumtext DEFAULT NULL COMMENT 'JSON que relaciona como se debe mostrar un determinado campo, por ejemplo Venta -> Preventa',
    `title_seo` mediumtext DEFAULT NULL COMMENT 'JSON con el title para SEO en distintos idiomas',
    `desc_seo` mediumtext DEFAULT NULL,
    `key_str` varchar(50) DEFAULT NULL,
    `external_relations` mediumtext DEFAULT NULL COMMENT 'JSON con la relación a servicios externos',
    `generated_by` enum('human','ai') NOT NULL DEFAULT 'human'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


ALTER TABLE `propiedades`
    ADD PRIMARY KEY (`clave_sistema`),
  ADD UNIQUE KEY `key_str` (`key_str`),
  ADD KEY `contrato` (`contrato`),
  ADD KEY `tipo` (`tipo`),
  ADD KEY `colonia` (`colonia`),
  ADD KEY `asesor` (`asesor`),
  ADD KEY `propiedades_prop_status` (`status_id`),
  ADD KEY `cmp_vta` (`comparto_comision`,`enventa`),
  ADD KEY `cmp_rta` (`rta_comparto_comision`,`enrenta`),
  ADD KEY `id_op` (`clave_sistema`,`operacion_hecha`),
  ADD KEY `idx_owner` (`clave_sistema`,`contrato`,`status_id`,`fecha_expiracion`),
  ADD KEY `idx_venta` (`enventa`,`precio_venta`),
  ADD KEY `idx_renta` (`enrenta`,`precio_renta`),
  ADD KEY `idx_diaria` (`endiaria`,`precio_diaria`),
  ADD KEY `idx_partners` (`clave_sistema`,`contrato`,`status_id`),
  ADD KEY `propiedades_index` (`clave_sistema`,`contrato`,`operacion_hecha`,`comparto_comision`,`rta_comparto_comision`,`enventa`,`enrenta`,`endiaria`,`status_web`,`fecha_expiracion`,`precio_venta`,`precio_renta`,`precio_diaria`),
  ADD KEY `propiedades_sucursales_foreign` (`sucursal`);


ALTER TABLE `propiedades`
    MODIFY `clave_sistema` bigint(20) NOT NULL AUTO_INCREMENT;


ALTER TABLE `propiedades`
    ADD CONSTRAINT `propiedades_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `propiedades_prop_status` FOREIGN KEY (`status_id`) REFERENCES `prop_status` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `propiedades_sucursales_foreign` FOREIGN KEY (`sucursal`) REFERENCES `sucursales` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
COMMIT;
