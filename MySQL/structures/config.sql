SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

CREATE TABLE `config` (
    `contrato` int(9) NOT NULL DEFAULT 0,
    `usuario` varchar(25) NOT NULL DEFAULT '',
    `meteor_id` varchar(50) DEFAULT NULL,
    `correo_ventas` varchar(60) NOT NULL DEFAULT '',
    `correo_contratacion` varchar(60) NOT NULL DEFAULT '',
    `correo_comentarios` varchar(60) NOT NULL DEFAULT '',
    `correo_dirgral` varchar(60) NOT NULL DEFAULT '',
    `dominio` varchar(50) DEFAULT NULL,
    `dominio2` varchar(50) DEFAULT NULL,
    `redes_sociales` mediumtext DEFAULT NULL COMMENT 'JSON con los usuarios para diferentes redes sociales, se usa par render sobre todo',
    `ruta` varchar(100) NOT NULL DEFAULT '',
    `inmuebles` int(5) NOT NULL DEFAULT 99999,
    `foto_ancho` int(3) NOT NULL DEFAULT 320,
    `por_ciudades` enum('Si','No') NOT NULL DEFAULT 'Si',
    `prop_por_pag` int(2) NOT NULL DEFAULT 7,
    `prop_por_linea` int(1) NOT NULL DEFAULT 1,
    `tipo_tema` enum('Generico','Personalizado') NOT NULL DEFAULT 'Personalizado',
    `theme` varchar(30) NOT NULL DEFAULT 'Default',
    `theme_hash` varchar(100) DEFAULT NULL,
    `theme_dev` mediumtext DEFAULT NULL,
    `nueva_ventana` enum('Si','No') NOT NULL DEFAULT 'Si',
    `ligas_comunes` enum('Si','No') NOT NULL DEFAULT 'Si',
    `ultimo_acceso` timestamp NULL DEFAULT NULL,
    `combinar_ampi` enum('Si','No') NOT NULL DEFAULT 'No',
    `notifica_telefono` enum('Si','No') NOT NULL DEFAULT 'Si',
    `telefono_props` enum('Si','No') NOT NULL DEFAULT 'No',
    `transparencia_fotos` enum('Si','No') NOT NULL DEFAULT 'Si',
    `tipo_fotos` enum('galeria','navegador','ambos') NOT NULL DEFAULT 'ambos',
    `fotos_por_linea` int(2) NOT NULL DEFAULT 4,
    `tipo_galeria` enum('dinamica','estatica') NOT NULL DEFAULT 'dinamica',
    `socio_ampi` enum('Si','No') NOT NULL DEFAULT 'No',
    `funcion_ampi` int(2) NOT NULL DEFAULT 0,
    `ampi_admin` enum('Si','No') NOT NULL DEFAULT 'No',
    `estado` varchar(50) NOT NULL DEFAULT '',
    `orden_inmuebles` enum('precio','colonia','fecha','claves') NOT NULL DEFAULT 'fecha',
    `forma_orden_inmuebles` enum('ascendente','descendente') NOT NULL DEFAULT 'descendente',
    `enviar_nextel` enum('Si','No') NOT NULL DEFAULT 'Si',
    `fotos` enum('purplehaze','otro') NOT NULL DEFAULT 'otro',
    `moneda_predeterminada` char(3) NOT NULL DEFAULT 'MXP',
    `mostrar_monedas` varchar(250) NOT NULL DEFAULT 'MXP,USD',
    `idioma` enum('esp','ing') NOT NULL DEFAULT 'esp',
    `campos_por_linea` int(2) NOT NULL DEFAULT 2,
    `leyenda_b_favoritos` enum('Si','No') NOT NULL DEFAULT 'Si',
    `lat_club_negocios` enum('Si','No') NOT NULL DEFAULT 'Si',
    `ultimo_cobro` date DEFAULT NULL,
    `dias_novedades` int(3) NOT NULL DEFAULT 30,
    `notificar_propietarios` enum('Si','No') NOT NULL DEFAULT 'No',
    `detalles_new` enum('Si','No') NOT NULL DEFAULT 'No',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Determina el status del contrato',
    `servicio_contratado` varchar(15) NOT NULL DEFAULT 'SI-M-7001',
    `pagado_hasta` date DEFAULT NULL,
    `vencido_si` enum('Si','No') NOT NULL DEFAULT 'No',
    `vencimiento` date DEFAULT NULL,
    `prorroga` date DEFAULT NULL,
    `saldo` double(9,2) NOT NULL DEFAULT 0.00,
    `adeudo` double(9,2) NOT NULL DEFAULT 0.00,
    `a_vencer` double(9,2) NOT NULL DEFAULT 0.00,
    `saldo_a_favor` double(9,2) NOT NULL DEFAULT 0.00,
    `credito` double(9,2) NOT NULL DEFAULT 0.00,
    `msg_panel_desactivado` date DEFAULT NULL,
    `presentar_con` enum('fotos','tour') NOT NULL DEFAULT 'fotos',
    `front` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Determina si tiene front (website) activo o inactivo el contrato',
    `deptID_livephp` int(10) DEFAULT NULL,
    `custom_data` mediumtext DEFAULT NULL COMMENT 'JSON con estructura de datos a renderizar en los templates',
    `external_connections` mediumtext DEFAULT NULL COMMENT 'JSON con los API_KEY de servicios de externos (portales, etc)',
    `auth_token` varchar(100) DEFAULT NULL COMMENT 'Token temporal para autenticación automática al panel',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


ALTER TABLE `config`
    ADD PRIMARY KEY (`contrato`) USING BTREE,
  ADD UNIQUE KEY `usuario` (`usuario`),
  ADD UNIQUE KEY `dominio` (`dominio`),
  ADD UNIQUE KEY `deptID_livephp` (`deptID_livephp`),
  ADD UNIQUE KEY `theme_hash` (`theme_hash`),
  ADD KEY `dominio2` (`dominio2`),
  ADD KEY `activos` (`status`,`pagado_hasta`,`prorroga`),
  ADD KEY `status` (`status`);


ALTER TABLE `config`
    ADD CONSTRAINT `contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `publiweb`.`contratos` (`numero`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;
