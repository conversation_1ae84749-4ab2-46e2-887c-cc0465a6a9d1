SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

CREATE TABLE `valores_campos` (
    `id` bigint(15) NOT NULL,
    `contrato` int(9) NOT NULL DEFAULT 0,
    `clave_sistema` bigint(20) NOT NULL,
    `variable_id` int(9) NOT NULL,
    `variable` varchar(25) NOT NULL DEFAULT '',
    `valor` mediumtext DEFAULT NULL,
    `valor_esp` mediumtext DEFAULT NULL,
    `valor_ing` mediumtext DEFAULT NULL,
    `valor_fra` mediumtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


ALTER TABLE `valores_campos`
    ADD PRIMARY KEY (`id`),
  ADD KEY `clave_sistema` (`clave_sistema`),
  ADD KEY `contrato` (`contrato`),
  ADD KEY `variable` (`variable`),
  ADD KEY `valores_campos_variable_id_campos_inmuebles_foreign` (`variable_id`);


ALTER TABLE `valores_campos`
    MODIFY `id` bigint(15) NOT NULL AUTO_INCREMENT;


ALTER TABLE `valores_campos`
    ADD CONSTRAINT `valores_campos_clave_sistema_propiedades_foreign` FOREIGN KEY (`clave_sistema`) REFERENCES `propiedades` (`clave_sistema`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `valores_campos_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `valores_campos_variable_id_campos_inmuebles_foreign` FOREIGN KEY (`variable_id`) REFERENCES `campos_inmuebles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;
