SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

CREATE TABLE `campos_inmuebles` (
    `id` int(9) NOT NULL,
    `contrato` int(9) DEFAULT NULL,
    `grupo_id` int(9) DEFAULT 1,
    `uso` enum('publico','interno') NOT NULL DEFAULT 'publico',
    `variable` varchar(35) NOT NULL DEFAULT '',
    `nombre` mediumtext DEFAULT NULL,
    `nombre_esp` varchar(35) NOT NULL DEFAULT '',
    `nombre_ing` varchar(35) NOT NULL DEFAULT '',
    `nombre_fra` varchar(35) NOT NULL DEFAULT '',
    `tipo_inmueble` int(3) NOT NULL DEFAULT 0,
    `tipo` enum('caracter','numerico','selector','texto') NOT NULL DEFAULT 'caracter',
    `longitud` varchar(250) NOT NULL DEFAULT '',
    `decimales` int(2) NOT NULL DEFAULT 0,
    `complemento_al_valor` varchar(10) NOT NULL DEFAULT '',
    `nulo` enum('Si','No') NOT NULL DEFAULT 'Si',
    `orden` double(4,1) NOT NULL DEFAULT 0.0,
    `activo` enum('Si','No') NOT NULL DEFAULT 'Si',
    `forzoso` enum('Si','No') NOT NULL DEFAULT 'No',
    `ti_relation` mediumtext DEFAULT NULL COMMENT 'JSON que establece con cuales tipos de inmuebles se relaciona, si es NULL o vacío, se relaciona con TODOS',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


ALTER TABLE `campos_inmuebles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `contrato_variable` (`contrato`,`variable`),
  ADD KEY `contrato` (`contrato`),
  ADD KEY `campos_inmuebles_campos_inmuebles_grupo` (`grupo_id`);


ALTER TABLE `campos_inmuebles`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;


ALTER TABLE `campos_inmuebles`
  ADD CONSTRAINT `campos_inmuebles_campos_inmuebles_grupo` FOREIGN KEY (`grupo_id`) REFERENCES `campos_inmuebles_grupos` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `campos_inmuebles_contrato_config_foreign` FOREIGN KEY (`contrato`) REFERENCES `config` (`contrato`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;
