<?php

declare(strict_types=1);

namespace App\Infrastructure\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Modelo para la tabla valores_campos
 * 
 * @property int $id
 * @property int $contrato
 * @property int $clave_sistema
 * @property int $variable_id
 * @property string $variable
 * @property string $valor
 * @property string $valor_esp
 * @property string $valor_ing
 * @property string $valor_fra
 */
class SIValorCampo extends Model
{
    protected $table = 'valores_campos';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'contrato',
        'clave_sistema',
        'variable_id',
        'variable',
        'valor',
        'valor_esp',
        'valor_ing',
        'valor_fra'
    ];

    protected $casts = [
        'contrato' => 'integer',
        'clave_sistema' => 'integer',
        'variable_id' => 'integer'
    ];

    /**
     * Relación con el inmueble
     */
    public function inmueble(): BelongsTo
    {
        return $this->belongsTo(SIInmueble::class, 'clave_sistema', 'clave_sistema');
    }

    /**
     * Relación con el campo
     */
    public function campo(): BelongsTo
    {
        return $this->belongsTo(SICampoInmueble::class, 'variable_id', 'id');
    }

    /**
     * Relación con la configuración del contrato
     */
    public function config(): BelongsTo
    {
        return $this->belongsTo(SIConfig::class, 'contrato', 'contrato');
    }

    /**
     * Obtiene el valor en el idioma especificado
     */
    public function getValorEnIdioma(string $idioma = 'esp'): ?string
    {
        return match($idioma) {
            'ing' => $this->valor_ing ?: $this->valor_esp,
            'fra' => $this->valor_fra ?: $this->valor_esp,
            default => $this->valor_esp ?: $this->valor
        };
    }

    /**
     * Obtiene el valor formateado con su complemento
     */
    public function getValorFormateado(string $idioma = 'esp'): ?string
    {
        $valor = $this->getValorEnIdioma($idioma);
        
        if (empty($valor)) {
            return null;
        }

        $complemento = $this->campo?->complemento_al_valor;
        
        if (empty($complemento)) {
            return $valor;
        }

        return $valor . ' ' . $complemento;
    }

    /**
     * Verifica si el valor está vacío
     */
    public function estaVacio(): bool
    {
        return empty($this->valor_esp) && empty($this->valor) && empty($this->valor_ing) && empty($this->valor_fra);
    }
}
