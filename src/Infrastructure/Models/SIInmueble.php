<?php

declare(strict_types=1);

namespace App\Infrastructure\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * Modelo extendido para inmuebles con campos personalizados
 *
 * @property int $clave_sistema
 * @property int $contrato
 * @property string $claveprop
 * @property string $nombreprop
 * @property int $tipo
 * @property string $colonia
 * @property int $sucursal
 * @property string $residencial
 * @property string $comercial
 * @property string $industrial
 * @property string $vacacional
 * @property int $precio_venta
 * @property int $precio_renta
 * @property int $precio_diaria
 * @property string $moneda
 * @property string $enventa
 * @property string $enrenta
 * @property string $endiaria
 * @property string $entraspaso
 * @property string $ciudad
 * @property string $provincia
 * @property string $pais
 * @property string $intro_corta_esp
 * @property string $intro_corta_ing
 * @property string $anuncio_esp
 * @property string $anuncio_ing
 * @property string $status_web
 * @property string $fecha_ingreso
 * @property string $map_lat
 * @property string $map_lng
 */
class SIInmueble extends Model
{
    protected $table = 'propiedades';
    protected $primaryKey = 'clave_sistema';
    public $timestamps = false;

    protected $fillable = [
        'clave_sistema',
        'contrato',
        'claveprop',
        'nombreprop',
        'tipo',
        'colonia',
        'sucursal',
        'residencial',
        'comercial',
        'industrial',
        'vacacional',
        'precio_venta',
        'precio_renta',
        'precio_diaria',
        'moneda',
        'enventa',
        'enrenta',
        'endiaria',
        'entraspaso',
        'ciudad',
        'provincia',
        'pais',
        'intro_corta_esp',
        'intro_corta_ing',
        'anuncio_esp',
        'anuncio_ing',
        'status_web',
        'map_lat',
        'map_lng'
    ];

    protected $casts = [
        'clave_sistema' => 'integer',
        'contrato' => 'integer',
        'tipo' => 'integer',
        'sucursal' => 'integer',
        'precio_venta' => 'integer',
        'precio_renta' => 'integer',
        'precio_diaria' => 'integer',
        'fecha_ingreso' => 'datetime'
    ];

    /**
     * Relación con la sucursal
     */
    public function sucursal(): BelongsTo
    {
        return $this->belongsTo(SISucursal::class, 'sucursal', 'id');
    }

    /**
     * Relación con la configuración del contrato
     */
    public function config(): BelongsTo
    {
        return $this->belongsTo(SIConfig::class, 'contrato', 'contrato');
    }

    /**
     * Relación con los valores de campos personalizados
     */
    public function valoresCampos(): HasMany
    {
        return $this->hasMany(SIValorCampo::class, 'clave_sistema', 'clave_sistema');
    }

    /**
     * Obtiene los campos personalizados organizados por grupos
     */
    public function getCamposPersonalizados(string $idioma = 'esp'): array
    {
        $valores = $this->valoresCampos()
            ->with(['campo' => function($query) {
                $query->activos()->publicos()->ordenados();
            }])
            ->get()
            ->filter(function($valor) {
                return $valor->campo && !$valor->estaVacio();
            });

        $grupos = [];

        foreach ($valores as $valor) {
            $campo = $valor->campo;

            // Verificar compatibilidad con tipo de inmueble
            if (!$campo->esCompatibleConTipo($this->tipo)) {
                continue;
            }

            $grupos[] = [
                'variable' => $campo->variable,
                'nombre' => $campo->getNombreEnIdioma($idioma),
                'valor' => $valor->getValorFormateado($idioma),
                'tipo' => $campo->tipo,
                'orden' => $campo->orden,
                'grupo_id' => $campo->grupo_id
            ];
        }

        // Ordenar por orden
        usort($grupos, fn($a, $b) => $a['orden'] <=> $b['orden']);

        return $grupos;
    }

    /**
     * Obtiene información completa del inmueble para la API
     */
    public function getInformacionCompleta(string $idioma = 'esp'): array
    {
        return [
            'id' => $this->clave_sistema,
            'clave_interna' => $this->claveprop,
            'nombre' => $this->nombreprop,
            'tipo' => $this->tipo,
            'ubicacion' => [
                'colonia' => $this->colonia,
                'ciudad' => $this->ciudad,
                'provincia' => $this->provincia,
                'pais' => $this->pais,
                'coordenadas' => [
                    'lat' => $this->map_lat,
                    'lng' => $this->map_lng
                ]
            ],
            'precios' => [
                'venta' => $this->precio_venta,
                'renta' => $this->precio_renta,
                'diaria' => $this->precio_diaria,
                'moneda' => $this->moneda
            ],
            'disponibilidad' => [
                'venta' => $this->enventa === 'Si',
                'renta' => $this->enrenta === 'Si',
                'diaria' => $this->endiaria === 'Si',
                'traspaso' => $this->entraspaso === 'Si'
            ],
            'categorias' => [
                'residencial' => $this->residencial === 'Si',
                'comercial' => $this->comercial === 'Si',
                'industrial' => $this->industrial === 'Si',
                'vacacional' => $this->vacacional === 'Si'
            ],
            'descripcion' => [
                'intro_corta' => $idioma === 'ing' ? $this->intro_corta_ing : $this->intro_corta_esp,
                'anuncio' => $idioma === 'ing' ? $this->anuncio_ing : $this->anuncio_esp
            ],
            'sucursal' => $this->sucursal ? [
                'id' => $this->sucursal->id,
                'nombre' => $this->sucursal->nombre,
                'telefono' => $this->sucursal->telefono,
                'email' => $this->sucursal->email
            ] : null,
            'campos_personalizados' => $this->getCamposPersonalizados($idioma),
            'estado' => $this->status_web,
            'fecha_ingreso' => $this->fecha_ingreso?->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Scope para inmuebles del owner
     */
    public function scopeDelOwner($query, int $contratoId)
    {
        return $query->where('contrato', $contratoId);
    }

    /**
     * Scope para inmuebles publicados
     */
    public function scopePublicados($query)
    {
        return $query->where('status_web', 'publicado');
    }
}