<?php

declare(strict_types=1);

namespace App\Infrastructure\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Modelo para la tabla campos_inmuebles
 * 
 * @property int $id
 * @property int $contrato
 * @property int $grupo_id
 * @property string $uso
 * @property string $variable
 * @property string $nombre
 * @property string $nombre_esp
 * @property string $nombre_ing
 * @property string $nombre_fra
 * @property int $tipo_inmueble
 * @property string $tipo
 * @property string $longitud
 * @property int $decimales
 * @property string $complemento_al_valor
 * @property string $nulo
 * @property float $orden
 * @property string $activo
 * @property string $forzoso
 * @property string $ti_relation
 */
class SICampoInmueble extends Model
{
    protected $table = 'campos_inmuebles';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'contrato',
        'grupo_id',
        'uso',
        'variable',
        'nombre',
        'nombre_esp',
        'nombre_ing',
        'nombre_fra',
        'tipo_inmueble',
        'tipo',
        'longitud',
        'decimales',
        'complemento_al_valor',
        'nulo',
        'orden',
        'activo',
        'forzoso',
        'ti_relation'
    ];

    protected $casts = [
        'ti_relation' => 'array',
        'orden' => 'float',
        'decimales' => 'integer',
        'tipo_inmueble' => 'integer',
        'grupo_id' => 'integer',
        'contrato' => 'integer'
    ];

    /**
     * Relación con la configuración del contrato
     */
    public function config(): BelongsTo
    {
        return $this->belongsTo(SIConfig::class, 'contrato', 'contrato');
    }

    /**
     * Relación con los valores de este campo
     */
    public function valores(): HasMany
    {
        return $this->hasMany(SIValorCampo::class, 'variable_id', 'id');
    }

    /**
     * Scope para campos públicos
     */
    public function scopePublicos($query)
    {
        return $query->where('uso', 'publico');
    }

    /**
     * Scope para campos activos
     */
    public function scopeActivos($query)
    {
        return $query->where('activo', 'Si');
    }

    /**
     * Scope para ordenar por orden
     */
    public function scopeOrdenados($query)
    {
        return $query->orderBy('orden', 'asc');
    }

    /**
     * Verifica si el campo es compatible con un tipo de inmueble
     */
    public function esCompatibleConTipo(int $tipoInmueble): bool
    {
        if (empty($this->ti_relation)) {
            return true; // Si no hay restricción, es compatible con todos
        }

        $tipoStr = str_pad((string)$tipoInmueble, 2, '0', STR_PAD_LEFT);
        return in_array($tipoStr, $this->ti_relation);
    }

    /**
     * Obtiene el nombre del campo en el idioma especificado
     */
    public function getNombreEnIdioma(string $idioma = 'esp'): string
    {
        return match($idioma) {
            'ing' => $this->nombre_ing ?: $this->nombre_esp,
            'fra' => $this->nombre_fra ?: $this->nombre_esp,
            default => $this->nombre_esp
        };
    }
}
