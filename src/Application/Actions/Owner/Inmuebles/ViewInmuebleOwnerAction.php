<?php

declare(strict_types=1);

namespace App\Application\Actions\Owner\Inmuebles;

use App\Application\Actions\Owner\OwnerAction;
use App\Application\Services\InmuebleAnalyticsService;
use App\Infrastructure\Models\SIInmueble;
use Psr\Http\Message\ResponseInterface as Response;
use Slim\Exception\HttpNotFoundException;
use Slim\Exception\HttpForbiddenException;

/**
 * Acción para obtener información detallada de un inmueble específico
 * 
 * Esta acción maneja la ruta GET /owner/inmuebles/{id} y devuelve:
 * - Información básica del inmueble
 * - Campos personalizados organizados (terreno, construcción, recámaras, etc.)
 * - Información de la sucursal
 * - Precios y disponibilidad
 * - Ubicación y coordenadas
 * - Descripción multiidioma
 */
class ViewInmuebleOwnerAction extends OwnerAction
{
    private InmuebleAnalyticsService $analyticsService;

    public function __construct($container)
    {
        parent::__construct($container);
        $this->analyticsService = new InmuebleAnalyticsService();
    }

    protected function action(): Response
    {
        // Obtener el ID del inmueble desde la ruta
        $inmuebleId = (int) $this->resolveArg('id');
        
        // Obtener parámetros de consulta
        $params = $this->request->getQueryParams();
        $idioma = $params['lang'] ?? $params['idioma'] ?? 'esp';
        $incluirCampos = filter_var($params['incluir_campos'] ?? 'true', FILTER_VALIDATE_BOOLEAN);
        $incluirSucursal = filter_var($params['incluir_sucursal'] ?? 'true', FILTER_VALIDATE_BOOLEAN);
        $incluirAnalytics = filter_var($params['incluir_analytics'] ?? 'false', FILTER_VALIDATE_BOOLEAN);
        $incluirComparacion = filter_var($params['incluir_comparacion'] ?? 'false', FILTER_VALIDATE_BOOLEAN);
        $incluirSugerencias = filter_var($params['incluir_sugerencias'] ?? 'false', FILTER_VALIDATE_BOOLEAN);
        
        // Validar idioma
        $idiomasPermitidos = ['esp', 'ing', 'fra'];
        if (!in_array($idioma, $idiomasPermitidos)) {
            $idioma = 'esp';
        }

        $this->logger->info("ViewInmuebleOwnerAction -> Consultando inmueble", [
            'inmueble_id' => $inmuebleId,
            'contrato_id' => $this->auth->getContratoId(),
            'idioma' => $idioma,
            'incluir_campos' => $incluirCampos,
            'incluir_sucursal' => $incluirSucursal
        ]);

        // Buscar el inmueble
        $query = SIInmueble::delOwner($this->auth->getContratoId())
            ->where('clave_sistema', $inmuebleId);

        // Incluir relaciones según parámetros
        if ($incluirCampos) {
            $query->with(['valoresCampos.campo' => function($q) {
                $q->activos()->publicos()->ordenados();
            }]);
        }

        if ($incluirSucursal) {
            $query->with('sucursal');
        }

        $inmueble = $query->first();

        // Verificar que el inmueble existe
        if (!$inmueble) {
            $this->logger->warning("Inmueble no encontrado", [
                'inmueble_id' => $inmuebleId,
                'contrato_id' => $this->auth->getContratoId()
            ]);
            
            throw new HttpNotFoundException(
                $this->request,
                "Inmueble con ID {$inmuebleId} no encontrado"
            );
        }

        // Verificar que pertenece al owner
        if ($inmueble->contrato !== $this->auth->getContratoId()) {
            $this->logger->warning("Acceso denegado a inmueble", [
                'inmueble_id' => $inmuebleId,
                'inmueble_contrato' => $inmueble->contrato,
                'owner_contrato' => $this->auth->getContratoId()
            ]);
            
            throw new HttpForbiddenException(
                $this->request,
                "No tienes permisos para acceder a este inmueble"
            );
        }

        // Obtener información completa
        $data = $inmueble->getInformacionCompleta($idioma);

        // Agregar metadatos adicionales
        $data['metadata'] = [
            'idioma_solicitado' => $idioma,
            'campos_incluidos' => $incluirCampos,
            'sucursal_incluida' => $incluirSucursal,
            'total_campos_personalizados' => count($data['campos_personalizados']),
            'fecha_consulta' => now()->toISOString(),
            'version_api' => '2.0'
        ];

        // Agregar estadísticas útiles
        $data['estadisticas'] = $this->getEstadisticasInmueble($inmueble);

        // Agregar análisis avanzado si se solicita
        if ($incluirAnalytics) {
            $data['analytics'] = [
                'completitud' => $this->analyticsService->analizarCompletitud($inmueble),
                'metricas' => $this->analyticsService->calcularMetricas($inmueble)
            ];
        }

        // Agregar comparación con similares si se solicita
        if ($incluirComparacion) {
            $data['comparacion'] = $this->analyticsService->compararConSimilares($inmueble);
        }

        // Agregar sugerencias de mejora si se solicita
        if ($incluirSugerencias) {
            $data['sugerencias'] = $this->analyticsService->generarSugerencias($inmueble);
        }

        // Agregar enlaces relacionados
        $data['enlaces'] = [
            'self' => "/owner/inmuebles/{$inmuebleId}",
            'editar' => "/owner/inmuebles/{$inmuebleId}/edit",
            'fotos' => "/owner/inmuebles/{$inmuebleId}/fotos",
            'preguntas' => "/owner/preguntas-inmuebles?inmueble_id={$inmuebleId}",
            'listado' => "/owner/inmuebles",
            'analytics' => "/owner/inmuebles/{$inmuebleId}?incluir_analytics=true",
            'comparacion' => "/owner/inmuebles/{$inmuebleId}?incluir_comparacion=true",
            'sugerencias' => "/owner/inmuebles/{$inmuebleId}?incluir_sugerencias=true"
        ];

        $this->logger->info("Inmueble consultado exitosamente", [
            'inmueble_id' => $inmuebleId,
            'total_campos' => count($data['campos_personalizados'])
        ]);

        return $this->respondWithData($data);
    }

    /**
     * Obtiene estadísticas útiles del inmueble
     */
    private function getEstadisticasInmueble(SIInmueble $inmueble): array
    {
        $stats = [
            'dias_desde_ingreso' => null,
            'precio_por_m2_terreno' => null,
            'precio_por_m2_construccion' => null,
            'campos_completados' => 0,
            'campos_totales' => 0
        ];

        // Calcular días desde ingreso
        if ($inmueble->fecha_ingreso) {
            $stats['dias_desde_ingreso'] = now()->diffInDays($inmueble->fecha_ingreso);
        }

        // Obtener valores de terreno y construcción para cálculos
        $terreno = $inmueble->valoresCampos()
            ->where('variable', 'ci_terreno')
            ->first();
        
        $construccion = $inmueble->valoresCampos()
            ->where('variable', 'ci_construccion')
            ->first();

        // Calcular precio por m2
        if ($terreno && !empty($terreno->valor_esp) && $inmueble->precio_venta > 0) {
            $terrenoM2 = (float) $terreno->valor_esp;
            if ($terrenoM2 > 0) {
                $stats['precio_por_m2_terreno'] = round($inmueble->precio_venta / $terrenoM2, 2);
            }
        }

        if ($construccion && !empty($construccion->valor_esp) && $inmueble->precio_venta > 0) {
            $construccionM2 = (float) $construccion->valor_esp;
            if ($construccionM2 > 0) {
                $stats['precio_por_m2_construccion'] = round($inmueble->precio_venta / $construccionM2, 2);
            }
        }

        // Contar campos completados vs totales
        $camposDisponibles = $inmueble->valoresCampos()
            ->with('campo')
            ->get()
            ->filter(function($valor) use ($inmueble) {
                return $valor->campo && 
                       $valor->campo->activo === 'Si' && 
                       $valor->campo->uso === 'publico' &&
                       $valor->campo->esCompatibleConTipo($inmueble->tipo);
            });

        $stats['campos_totales'] = $camposDisponibles->count();
        $stats['campos_completados'] = $camposDisponibles->filter(function($valor) {
            return !$valor->estaVacio();
        })->count();

        return $stats;
    }
}
