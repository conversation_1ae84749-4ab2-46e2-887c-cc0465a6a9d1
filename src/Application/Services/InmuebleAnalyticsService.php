<?php

declare(strict_types=1);

namespace App\Application\Services;

use App\Infrastructure\Models\SIInmueble;
use App\Infrastructure\Models\SIValorCampo;
use Illuminate\Support\Collection;

/**
 * Servicio para análisis avanzado de inmuebles
 * 
 * Proporciona funcionalidades de análisis, comparación y estadísticas
 * para inmuebles y sus campos personalizados.
 */
class InmuebleAnalyticsService
{
    /**
     * Analiza la completitud de información de un inmueble
     */
    public function analizarCompletitud(SIInmueble $inmueble): array
    {
        $analisis = [
            'puntuacion_total' => 0,
            'puntuacion_maxima' => 100,
            'porcentaje_completitud' => 0,
            'areas' => [
                'informacion_basica' => ['puntos' => 0, 'maximo' => 25],
                'precios' => ['puntos' => 0, 'maximo' => 20],
                'ubicacion' => ['puntos' => 0, 'maximo' => 15],
                'descripcion' => ['puntos' => 0, 'maximo' => 20],
                'campos_personalizados' => ['puntos' => 0, 'maximo' => 20]
            ],
            'recomendaciones' => []
        ];

        // Analizar información básica
        $analisis['areas']['informacion_basica']['puntos'] += !empty($inmueble->nombreprop) ? 10 : 0;
        $analisis['areas']['informacion_basica']['puntos'] += !empty($inmueble->claveprop) ? 5 : 0;
        $analisis['areas']['informacion_basica']['puntos'] += $inmueble->tipo > 0 ? 10 : 0;

        if (empty($inmueble->nombreprop)) {
            $analisis['recomendaciones'][] = 'Agregar nombre descriptivo al inmueble';
        }

        // Analizar precios
        $tienePrecioVenta = $inmueble->precio_venta > 0 && $inmueble->enventa === 'Si';
        $tienePrecioRenta = $inmueble->precio_renta > 0 && $inmueble->enrenta === 'Si';
        $tienePrecioDiaria = $inmueble->precio_diaria > 0 && $inmueble->endiaria === 'Si';

        if ($tienePrecioVenta) $analisis['areas']['precios']['puntos'] += 10;
        if ($tienePrecioRenta) $analisis['areas']['precios']['puntos'] += 10;
        if (!empty($inmueble->moneda)) $analisis['areas']['precios']['puntos'] += 5;

        if (!$tienePrecioVenta && !$tienePrecioRenta && !$tienePrecioDiaria) {
            $analisis['recomendaciones'][] = 'Definir al menos un precio (venta, renta o diaria)';
        }

        // Analizar ubicación
        $analisis['areas']['ubicacion']['puntos'] += !empty($inmueble->colonia) ? 5 : 0;
        $analisis['areas']['ubicacion']['puntos'] += !empty($inmueble->ciudad) ? 5 : 0;
        $analisis['areas']['ubicacion']['puntos'] += !empty($inmueble->map_lat) && !empty($inmueble->map_lng) ? 5 : 0;

        if (empty($inmueble->map_lat) || empty($inmueble->map_lng)) {
            $analisis['recomendaciones'][] = 'Agregar coordenadas GPS para mejor ubicación';
        }

        // Analizar descripción
        $analisis['areas']['descripcion']['puntos'] += !empty($inmueble->intro_corta_esp) ? 10 : 0;
        $analisis['areas']['descripcion']['puntos'] += !empty($inmueble->anuncio_esp) ? 10 : 0;

        if (empty($inmueble->anuncio_esp)) {
            $analisis['recomendaciones'][] = 'Agregar descripción detallada del inmueble';
        }

        // Analizar campos personalizados
        $camposPersonalizados = $inmueble->getCamposPersonalizados();
        $totalCampos = count($camposPersonalizados);
        
        if ($totalCampos > 0) {
            $analisis['areas']['campos_personalizados']['puntos'] = min(20, $totalCampos * 2);
        }

        if ($totalCampos < 5) {
            $analisis['recomendaciones'][] = 'Completar más campos personalizados (terreno, construcción, recámaras, etc.)';
        }

        // Calcular puntuación total
        foreach ($analisis['areas'] as $area) {
            $analisis['puntuacion_total'] += $area['puntos'];
        }

        $analisis['porcentaje_completitud'] = round(
            ($analisis['puntuacion_total'] / $analisis['puntuacion_maxima']) * 100, 
            1
        );

        return $analisis;
    }

    /**
     * Compara un inmueble con otros similares del mismo contrato
     */
    public function compararConSimilares(SIInmueble $inmueble, int $limite = 5): array
    {
        $similares = SIInmueble::delOwner($inmueble->contrato)
            ->where('clave_sistema', '!=', $inmueble->clave_sistema)
            ->where('tipo', $inmueble->tipo)
            ->where('status_web', 'publicado')
            ->with(['valoresCampos.campo'])
            ->limit($limite)
            ->get();

        $comparacion = [
            'inmueble_base' => [
                'id' => $inmueble->clave_sistema,
                'nombre' => $inmueble->nombreprop,
                'precio_venta' => $inmueble->precio_venta,
                'precio_renta' => $inmueble->precio_renta
            ],
            'similares' => [],
            'estadisticas' => [
                'precio_venta_promedio' => 0,
                'precio_renta_promedio' => 0,
                'posicion_precio_venta' => null,
                'posicion_precio_renta' => null
            ]
        ];

        $preciosVenta = [$inmueble->precio_venta];
        $preciosRenta = [$inmueble->precio_renta];

        foreach ($similares as $similar) {
            $comparacion['similares'][] = [
                'id' => $similar->clave_sistema,
                'nombre' => $similar->nombreprop,
                'precio_venta' => $similar->precio_venta,
                'precio_renta' => $similar->precio_renta,
                'diferencia_precio_venta' => $similar->precio_venta - $inmueble->precio_venta,
                'diferencia_precio_renta' => $similar->precio_renta - $inmueble->precio_renta
            ];

            $preciosVenta[] = $similar->precio_venta;
            $preciosRenta[] = $similar->precio_renta;
        }

        // Calcular estadísticas
        $preciosVentaFiltrados = array_filter($preciosVenta, fn($p) => $p > 0);
        $preciosRentaFiltrados = array_filter($preciosRenta, fn($p) => $p > 0);

        if (!empty($preciosVentaFiltrados)) {
            $comparacion['estadisticas']['precio_venta_promedio'] = round(array_sum($preciosVentaFiltrados) / count($preciosVentaFiltrados));
            
            // Posición en ranking de precios
            rsort($preciosVentaFiltrados);
            $posicion = array_search($inmueble->precio_venta, $preciosVentaFiltrados);
            $comparacion['estadisticas']['posicion_precio_venta'] = $posicion !== false ? $posicion + 1 : null;
        }

        if (!empty($preciosRentaFiltrados)) {
            $comparacion['estadisticas']['precio_renta_promedio'] = round(array_sum($preciosRentaFiltrados) / count($preciosRentaFiltrados));
            
            rsort($preciosRentaFiltrados);
            $posicion = array_search($inmueble->precio_renta, $preciosRentaFiltrados);
            $comparacion['estadisticas']['posicion_precio_renta'] = $posicion !== false ? $posicion + 1 : null;
        }

        return $comparacion;
    }

    /**
     * Genera sugerencias de mejora para el inmueble
     */
    public function generarSugerencias(SIInmueble $inmueble): array
    {
        $sugerencias = [
            'prioritarias' => [],
            'recomendadas' => [],
            'opcionales' => []
        ];

        // Sugerencias prioritarias
        if (empty($inmueble->anuncio_esp)) {
            $sugerencias['prioritarias'][] = [
                'tipo' => 'descripcion',
                'mensaje' => 'Agregar descripción detallada aumenta las consultas en un 40%',
                'accion' => 'Escribir descripción atractiva del inmueble'
            ];
        }

        if ($inmueble->precio_venta <= 0 && $inmueble->precio_renta <= 0) {
            $sugerencias['prioritarias'][] = [
                'tipo' => 'precio',
                'mensaje' => 'Inmuebles sin precio reciben 80% menos consultas',
                'accion' => 'Definir precio de venta o renta'
            ];
        }

        // Sugerencias recomendadas
        $camposImportantes = ['ci_terreno', 'ci_construccion', 'ci_recamaras', 'ci_banos'];
        $camposCompletados = $inmueble->valoresCampos()
            ->whereIn('variable', $camposImportantes)
            ->whereNotNull('valor_esp')
            ->where('valor_esp', '!=', '')
            ->count();

        if ($camposCompletados < count($camposImportantes)) {
            $sugerencias['recomendadas'][] = [
                'tipo' => 'campos',
                'mensaje' => 'Completar campos básicos mejora la visibilidad',
                'accion' => 'Agregar información de terreno, construcción, recámaras y baños'
            ];
        }

        if (empty($inmueble->map_lat) || empty($inmueble->map_lng)) {
            $sugerencias['recomendadas'][] = [
                'tipo' => 'ubicacion',
                'mensaje' => 'Inmuebles con GPS reciben 25% más consultas',
                'accion' => 'Agregar coordenadas GPS precisas'
            ];
        }

        // Sugerencias opcionales
        if (empty($inmueble->intro_corta_ing)) {
            $sugerencias['opcionales'][] = [
                'tipo' => 'idioma',
                'mensaje' => 'Descripción en inglés amplía el mercado potencial',
                'accion' => 'Traducir descripción al inglés'
            ];
        }

        return $sugerencias;
    }

    /**
     * Calcula métricas de rendimiento del inmueble
     */
    public function calcularMetricas(SIInmueble $inmueble): array
    {
        $metricas = [
            'eficiencia_espacial' => null,
            'precio_por_m2_terreno' => null,
            'precio_por_m2_construccion' => null,
            'ratio_construccion_terreno' => null,
            'indice_completitud' => 0
        ];

        // Obtener valores de terreno y construcción
        $terreno = $inmueble->valoresCampos()
            ->where('variable', 'ci_terreno')
            ->first();
        
        $construccion = $inmueble->valoresCampos()
            ->where('variable', 'ci_construccion')
            ->first();

        if ($terreno && !empty($terreno->valor_esp)) {
            $terrenoM2 = (float) $terreno->valor_esp;
            
            if ($terrenoM2 > 0 && $inmueble->precio_venta > 0) {
                $metricas['precio_por_m2_terreno'] = round($inmueble->precio_venta / $terrenoM2, 2);
            }

            if ($construccion && !empty($construccion->valor_esp)) {
                $construccionM2 = (float) $construccion->valor_esp;
                
                if ($construccionM2 > 0) {
                    $metricas['ratio_construccion_terreno'] = round($construccionM2 / $terrenoM2, 3);
                    
                    if ($inmueble->precio_venta > 0) {
                        $metricas['precio_por_m2_construccion'] = round($inmueble->precio_venta / $construccionM2, 2);
                    }
                }
            }
        }

        // Calcular índice de completitud
        $analisis = $this->analizarCompletitud($inmueble);
        $metricas['indice_completitud'] = $analisis['porcentaje_completitud'];

        return $metricas;
    }
}
