<?php

declare(strict_types=1);

use App\Application\Actions\Owner\Bex\CitiesTypesBexLightOwnerAction;
use App\Application\Actions\Owner\Bex\CiudadesColoniasTiposBexOwnerAction;
use App\Application\Actions\Owner\Data\LogoDataOwnerAction;
use App\Application\Actions\Owner\Data\MenuDataOwnerAction;
use App\Application\Actions\Owner\Inmuebles\BuscadorExpressInmueblesOwnerAction;
use App\Application\Actions\Owner\Inmuebles\ListInmueblesOwnerAction;
use App\Application\Actions\Owner\Inmuebles\ViewInmuebleOwnerAction;
use App\Application\Actions\Owner\Socios\AutorizarSocioOwnerAction;
use App\Application\Actions\Owner\Socios\BuscarSociosOwnerAction;
use App\Application\Actions\Owner\Socios\CancelarSocioOwnerAction;
use App\Application\Actions\Owner\Socios\CreateTagOwnerAction;
use App\Application\Actions\Owner\Socios\DeleteRelationTagOwnerAction;
use App\Application\Actions\Owner\Socios\DeleteTagOwnerAction;
use App\Application\Actions\Owner\Socios\EliminarSocioOwnerAction;
use App\Application\Actions\Owner\Socios\InvitarActivacionSocioOwnerAction;
use App\Application\Actions\Owner\Socios\ListSociosOwnerAction;
use App\Application\Actions\Owner\Socios\RechazarSocioOwnerAction;
use App\Application\Actions\Owner\Socios\SolicitarSocioOwnerAction;
use App\Application\Actions\Pregunta\ListOwnerPreguntasAction;
use App\Application\Actions\Pregunta\ViewOwnerPreguntaAction;
use App\Application\Actions\Sucursal\ListOwnerSucursalesAction;
use App\Application\Actions\User\ListUsersAction;
use App\Application\Actions\User\ViewUserAction;
use App\Domain\Auth\AuthRepositoryInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

return function (App $app) {
    $app->options('/{routes:.*}', function (Request $request, Response $response) {
        // CORS Pre-Flight OPTIONS Request Handler
        return $response;
    });

    $app->get('/', function (Request $request, Response $response) use ($app) {
        /** @var AuthRepositoryInterface $authRepository */
        $authRepository = $app->getContainer()->get(AuthRepositoryInterface::class);

        $response->getBody()->write(
            'El contrato que se ha validado por medio de Token es: ' .
            $authRepository->getContratoId()
        );

        return $response;
    });

    //    $app->get('/render', function ($request, $response) {
    //        $mustache = new Mustache_Engine;
    //
    //        $template = file_get_contents(__DIR__ . '/../templates/example.mustache');
    //        $data = ['name' => 'Jane Doe'];
    //
    //        $rendered = $mustache->render($template, $data);
    //
    //        $response->getBody()->write($rendered);
    //        return $response->withHeader('Content-Type', 'text/html');
    //    });

    $app->group('/owner/inmuebles', function (Group $group) {
        $group->get('', ListInmueblesOwnerAction::class);
        $group->get('/buscador-express', BuscadorExpressInmueblesOwnerAction::class);

    });

    $app->group('/owner/bex', function (Group $group) {
        $group->get('/cities-types-light', CitiesTypesBexLightOwnerAction::class);
        $group->get('/cities-colonies-types', CiudadesColoniasTiposBexOwnerAction::class);
    });

    $app->group('/owner/preguntas-inmuebles', function (Group $group) {
        $group->get('', ListOwnerPreguntasAction::class);
        $group->get('/{id}', ViewOwnerPreguntaAction::class);
    });

    $app->group('/owner/sucursales', function (Group $group) {
        $group->get('', ListOwnerSucursalesAction::class);
    });

    $app->group('/owner/data', function (Group $group) {
        $group->get('/logo', LogoDataOwnerAction::class);
        $group->get('/menu', MenuDataOwnerAction::class);
    });

    $app->group('/owner/socios', function (Group $group) {
        $group->get('', ListSociosOwnerAction::class);
        $group->get('/buscar', BuscarSociosOwnerAction::class);
        $group->post('/invitacion', InvitarActivacionSocioOwnerAction::class);
        $group->post('/{id}/autorizar', AutorizarSocioOwnerAction::class);
        $group->post('/{id}/rechazar', RechazarSocioOwnerAction::class);
        $group->post('/{id}/cancelar', CancelarSocioOwnerAction::class);
        $group->post('/{id}/eliminar', EliminarSocioOwnerAction::class);
        $group->post('/{id}/solicitar', SolicitarSocioOwnerAction::class);
    });

    $app->group('/owner/tags-socios', function (Group $group) {
        $group->post('', CreateTagOwnerAction::class);
        $group->delete('/{socio_id}/{id}', DeleteRelationTagOwnerAction::class);
        $group->delete('/{id}', DeleteTagOwnerAction::class);
    });

    $app->group('/users', function (Group $group) {
        $group->get('', ListUsersAction::class);
        $group->get('/{id}', ViewUserAction::class);
    });
};
